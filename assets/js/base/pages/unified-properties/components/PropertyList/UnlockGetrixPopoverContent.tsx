import React from 'react';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { useSuspenseQuery } from '@tanstack/react-query';
import { getMeQueryOptions } from 'lib/REST/requests/rest/profile/query-factory';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { List } from '@gx-design/list';

export const UnlockGetrixPopoverContent: React.FC = () => {
    const meQuery = useSuspenseQuery(getMeQueryOptions);

    return (
        <div className="unlockGetrixPopover">
            <Icon className="unlockGetrixPopover__icon" name="unlock" />
            <h4 className="gx-title-2 unlockGetrixPopover__title">
                {trans('label.unlock_getrix_actions_title', {
                    GETRIX_APP_NAME: gtxConstants('GETRIX_APP_NAME'),
                })}
            </h4>
            <p className="gx-body-small unlockGetrixPopover__description">
                {trans('label.unlock_getrix_actions_text', {
                    GETRIX_APP_NAME: gtxConstants('GETRIX_APP_NAME'),
                })}
            </p>
            <h4 className="gx-body-small unlockGetrixPopover__subtitle">
                {trans('label.unlock_getrix_actions_subtitle')}
            </h4>
            <List className="unlockGetrixPopover__cta">
                <div className="gx-body-small">
                    <Icon
                        className="unlockGetrixPopover__ctaIcon"
                        name="phone"
                    />
                    <span className="unlockGetrixPopover__ctaPhone">
                        {meQuery.data?.responsible?.telefono ?? ''}
                    </span>
                </div>
                <div className="gx-body-small">
                    <Icon
                        className="unlockGetrixPopover__ctaIcon"
                        name="mail"
                    />
                    <span className="unlockGetrixPopover__ctaEmail">
                        {meQuery.data?.responsible?.email ?? ''}
                    </span>
                </div>
            </List>
        </div>
    );
};

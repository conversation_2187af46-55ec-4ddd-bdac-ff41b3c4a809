{"license": "MIT", "type": "project", "description": "MLS - Multi Layer System for Getrix", "authors": [{"name": "ImmobiliareLabs", "email": "<EMAIL>"}], "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "require": {"php": "^7.4", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-openssl": "*", "doctrine/annotations": "^1.7", "ekbl/stats-bundle": "^2.0", "firebase/php-jwt": "^1.0", "getrix-be/growthbook-bundle": "^1.3", "getrix/object-mapper": "^1.0", "getrix/pnformatter": "^0.0.8", "growthbook/growthbook": "^1.7", "guzzlehttp/guzzle": "^7.8", "guzzlehttp/psr7": "^2.7", "indomio-translations/cms": "1.83.3", "indomio-translations/cms-contents": "1.13.9", "maxakawizard/po-parser": "^1.2", "nesbot/carbon": "^1.39", "pepita/wsse-header-generator-bundle": "^1.0", "pepita/wsse-header-generator-php": "^1.0", "php-http/discovery": "^1.20", "phpdocumentor/reflection-docblock": "^4.3", "ramsey/uuid": "^4.2", "sensio/framework-extra-bundle": "^5.2", "sentry/sentry": "^1.9", "sentry/sentry-symfony": "^1.0", "soa/stubs": "20250604161148", "symfony/asset": "^3.4", "symfony/cache": "^3.4", "symfony/console": "^3.4", "symfony/css-selector": "^3.4", "symfony/dom-crawler": "^3.4", "symfony/dotenv": "^3.4", "symfony/expression-language": "^3.4", "symfony/flex": "^1.8", "symfony/form": "^3.4", "symfony/framework-bundle": "^3.4", "symfony/lts": "^3", "symfony/monolog-bundle": "^3.3", "symfony/property-info": "^3.4", "symfony/security-bundle": "^3.4", "symfony/serializer": "^3.4", "symfony/swiftmailer-bundle": "^3.2", "symfony/templating": "^3.4", "symfony/twig-bundle": "^3.4", "symfony/validator": "^3.4", "symfony/yaml": "^3.4", "twig/extensions": "^1.5", "whichbrowser/parser": "^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "^9.6", "symfony/browser-kit": "^3.4", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "^3.4", "symfony/web-profiler-bundle": "^3.4", "vimeo/psalm": "^4.30"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd"}, "pre-scripts": ["test -f public/bundles/manifest.json || echo '{}' > public/bundles/manifest.json", "test -f public/bundles/webpack-stats.json || echo '{}' > public/bundles/webpack-stats.json"], "post-scripts": ["bash ./vendor/indomio-translations/cms/bin/po2mo cms ./vendor/indomio-translations/cms/translations/messages translations symfony", "bash ./vendor/indomio-translations/cms-contents/bin/po2mo cms-contents ./vendor/indomio-translations/cms-contents/translations/messages translations symfony"], "pre-install-cmd": ["@pre-scripts"], "post-install-cmd": ["@post-scripts", "@auto-scripts"], "post-update-cmd": ["@post-scripts", "@auto-scripts"], "php-cs-fixer": "vendor/bin/php-cs-fixer fix --verbose", "php-cs-fixer-dry-run": "vendor/bin/php-cs-fixer fix --verbose --diff --dry-run", "psalm": "vendor/bin/psalm --no-cache --config=psalm.xml --no-file-cache --long-progress"}, "conflict": {"symfony/symfony": "*"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "secure-http": false, "allow-plugins": {"kylekatarnls/update-helper": true, "symfony/flex": true, "php-http/discovery": true}}, "extra": {"symfony": {"allow-contrib": false}}, "repositories": [{"type": "composer", "url": "https://satis.pepita.io"}], "version": "v6.71.1"}
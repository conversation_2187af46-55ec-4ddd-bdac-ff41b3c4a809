feature_toggle:
    groups:
        multisend:
            name: "multisend"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        acquisition:
            name: "acquisition"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        agency_estimates:
            name: "agency_estimates"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        extra_services:
            name: "extra_services"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        virtual_tour_360:
            name: "virtual_tour_360"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        agenda:
            name: "agenda"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        dashboard:
            name: "dashboard"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        administration:
            name: "administration"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        settings:
            name: "settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        clients:
            name: "clients"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
        customer_requests:
            name: "customer_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        telefono_smart:
            name: "telefono_smart"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        ads:
            name: "ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        sso:
            name: "sso"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
        visibility:
            name: "visibility"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
    features:
        multisend_property:
            name: "multisend_property"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "multisend"
        multisend_project:
            name: "multisend_project"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "multisend"
        multisend_portal:
            name: "multisend_portal"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "multisend"
        acquisition_privates:
            name: "acquisition_privates"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "acquisition"
        agency_estimates_add:
            name: "agency_estimates_add"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "agency_estimates"
        agency_estimates_list:
            name: "agency_estimates_list"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "agency_estimates"
        real_estate_evaluation:
            name: "real_estate_evaluation"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "acquisition"
        plans:
            name: "plans"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "extra_services"
        youdomus:
            name: "youdomus"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "extra_services"
        news:
            name: "news"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "extra_services"
        web_service:
            name: "web_service"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "extra_services"
        website:
            name: "website"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "extra_services"
        evaluation:
            name: "evaluation"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "extra_services"
        virtual_tour_360_property:
            name: "virtual_tour_360_property"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "virtual_tour_360"
        virtual_tour_360_project:
            name: "virtual_tour_360_project"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "virtual_tour_360"
        agenda:
            name: "agenda"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "agenda"
        dashboard:
            name: "dashboard"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "dashboard"
        invoices_info:
            name: "invoices_info"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "administration"
        contract_info:
            name: "contract_info"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "administration"
        invoices:
            name: "invoices"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "administration"
        general_settings:
            name: "general_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "settings"
        office_place_settings:
            name: "office_place_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "settings"
        images_video_settings:
            name: "images_video_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "settings"
        users_settings:
            name: "users_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "settings"
        immovisita_settings:
            name: "immovisita_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "settings"
        security_settings:
            name: "security_settings"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "settings"
        propertyfinder_search:
            name: "propertyfinder_search"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "propertyfinder"
        propertyfinder_collaboration:
            name: "propertyfinder_collaboration"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "propertyfinder"
        clients:
            name: "clients"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "clients"
        general_customer_requests:
            name: "general_customer_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "customer_requests"
        direct_customer_requests:
            name: "direct_customer_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "customer_requests"
        managed_customer_requests:
            name: "managed_customer_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "customer_requests"
        telefono_smart_requests:
            name: "telefono_smart_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "telefono_smart"
        zone_requests:
            name: "zone_requests"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "customer_requests"
        property_ads:
            name: "property_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        project_ads:
            name: "project_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        auctions_ads:
            name: "auctions_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        land_ads:
            name: "land_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        land_virtual_ads:
            name: "land_virtual_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "ads"
        residential_ads:
            name: "residential_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        commercial_ads:
            name: "commercial_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_DISABLED
            group: "ads"
        room_ads:
            name: "room_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        buildings_ads:
            name: "buildings_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        warehouse_ads:
            name: "warehouse_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        garage_ads:
            name: "garage_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        sheds_ads:
            name: "sheds_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        office_ads:
            name: "office_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        shop_ads:
            name: "shop_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        sold_rented_ads:
            name: "sold_rented_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "ads"
        sso_to_gtx:
            name: "sso_to_gtx"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "sso"
        luxury_ads:
            name: "luxury_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "visibility"
        foreign_ads:
            name: "foreign_ads"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "visibility"
        guaranteed_property:
            name: "guaranteed_property"
            enabled: !php/const App\Config\FeatureConfiguration::FEATURE_ENABLED
            group: "visibility"

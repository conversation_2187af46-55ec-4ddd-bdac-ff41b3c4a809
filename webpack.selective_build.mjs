import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv'
import { basicEntries } from './webpack.common.mjs'
import common from './webpack.common.mjs'
import { merge } from 'webpack-merge';
import { getEntriesCli } from './webpack.selective_common.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({
  path: path.join(__dirname, '.env.it')
})

export default async (env) => {

  const entries = await getEntriesCli(env)

  const mergedConfig = merge({
    ...common,
    entry: Object.assign({}, entries, basicEntries),
  }, {
    mode: process.env.NODE_ENV || 'production',
    optimization: {
      runtimeChunk: 'single',
      minimize: true,
      splitChunks: {
        chunks: 'all'
      }
    },
    devtool: "source-map",
  })

  return mergedConfig;
};
